<?php 
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2015 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
// 检测PHP环境
namespace think;
if (version_compare(PHP_VERSION, '7.4.0', '<'))
    die('require PHP > 7.4.0 !');
if (version_compare(PHP_VERSION, '8.0.0', '>='))
    die('require PHP < 8.0.0 !');

require __DIR__ . '/vendor/autoload.php';

define('BIND_MODULE', "install");

// 执行HTTP应用并响应
$http = (new App())->http;
$response = $http->run();
$response->send();
$http->end($response);



