{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "require": {"php": ">=7.4.0", "topthink/framework": "v6.0.14", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "1.0.14", "topthink/think-view": "1.0.14", "liliuwei/thinkphp-jump": "1.5", "topthink/think-captcha": "3.0.2", "overtrue/wechat": "4.4.1", "phpoffice/phpexcel": "^1.8", "phpoffice/phpspreadsheet": "1.24.1", "overtrue/easy-sms": "^1.3", "php-curl-class/php-curl-class": "8.9.3", "phpmailer/phpmailer": "6.4.1", "intervention/image": "2.5.1", "nesbot/carbon": "2.48.0", "thans/thinkphp-filesystem-cloud": "1.0.2", "ext-json": "*", "workerman/gatewayclient": "3.0.0", "wechatpay/wechatpay": "1.4.5", "topthink/think-queue": "^3.0", "lizhichao/word": "^2.1", "yunwuxin/think-cron": "^3.0", "ezyang/htmlpurifier": "^4.16"}, "require-dev": {"symfony/var-dumper": "4.4.41", "topthink/think-trace": "1.4"}, "autoload": {"psr-4": {"app\\": "app", "addon\\": "addon", "extend\\": "extend"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}