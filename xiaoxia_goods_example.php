<?php
require_once __DIR__ . '/vendor/autoload.php';
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 优化PHP配置以处理大JSON文件
ini_set('memory_limit', '256M');        // 增加内存限制
ini_set('max_execution_time', 0);       // 取消执行时间限制
$app = new think\App();
$app->initialize();

use app\model\xiaoxia\Main;

$xiaoxia = new Main();

/*// 示例1: 获取商品列表
$condition = [
    ['site_id', '=', 1],
    ['goods_state', '=', 1],
    ['is_delete', '=', 0]
];
$field = 'goods_id,goods_name,price';
$result = $xiaoxia->getGoodsList($condition, $field, 'goods_id desc', 5);

var_dump($result);

// 示例2: 筛选条件
$condition2 = [
    ['site_id', '=', 1],
    ['price', '>', 100]
];
$result2 = $xiaoxia->getGoodsList($condition2, '*', 'price asc', 3);

var_dump($result2);

// 示例3: 修改商品
$update_condition = [
    ['goods_id', '=', 1],
    ['site_id', '=', 1]
];
$update_data = [
    'goods_name' => '新的商品名称',
    'price' => 199.99,
    'goods_state' => 1
];
$result3 = $xiaoxia->updateGoods($update_condition, $update_data);

var_dump($result3);*/

// 示例4: 批量修改排序
/*$site_id = 1;
$sort_data = file_get_contents(__DIR__ .'/sort_data.json');
$sort_data = json_decode($sort_data, true);
$result4 = $xiaoxia->batchUpdateSort($site_id, $sort_data);
var_dump($result4);*/


// 获取全部商品
$condition = [
    ['site_id', '=', 1],
    ['goods_state', '=', 1],
    ['is_delete', '=', 0]
];
$field = 'goods_id,goods_name,price,wb_spuId';
$result = $xiaoxia->getGoodsList($condition, $field, 'goods_id desc', );

// 循环 $result
foreach ($result['data'] as $key => $value) {
    $wb_spuId= $value['wb_spuId'];
    $goods_id = $value['goods_id'];
    if(!empty($wb_spuId)){
        // 请求接口 获取评价值
        $res = file_get_contents('http://192.168.3.205:10001/index.php/api/Sams.SamsGoodsReview/getEvaluateBySpuId?spu_id='.$wb_spuId);
        $res = json_decode($res, true);
        $evaluate = $res['data']['evaluate_data'] ?? [];
        // 简单 循环遍历res
        foreach ($evaluate as $item) {
            $commentImageUrls = $item['commentImageUrls'];
            $member_name = $item['userName'];
            $member_headimg = $item['userHeadUrl'];
            $commentContent = $item['commentContent'];
            $commentTime = $item['commentTime'] / 1000  ??  time();
            $evaluate_data = [
                'site_id' => 1,
                'scores' => 5,
                'explain_type' => 1, // 1:好评 2:中评 3:差评
                'is_audit' => 1,
                'goods_id' => $goods_id,
                'member_name' => $member_name,
                'member_headimg'=>$member_headimg,
                'content' => $commentContent,
                'images' => $commentImageUrls,
                'create_time' =>$commentTime,

            ];
            $result5 = $xiaoxia->addGoodsEvaluate($evaluate_data);
            var_dump($result5);
        }
    }
}







?>
