<?php
// +----------------------------------------------------------------------
// | 店铺端菜单设置
// +----------------------------------------------------------------------

return [
    [
        'name' => 'INDEX_ROOT',
        'title' => '概况',
        'url' => 'shop/index/index',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'icongaikuang',
        'picture_selected' => '',
        'sort' => 1,
    ],
    [
        'name' => 'SHOP_ROOT',
        'title' => '店铺',
        'url' => 'shop/diy/management',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'icondianpu',
        'picture_selected' => '',
        'sort' => 2,
        'child_list' => [
            [
                'name' => 'SHOP_DIY',
                'title' => '手机端',
                'url' => 'shop/diy/management',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/diy_wap_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/diy_wap_select.png',
                'sort' => 2,
                'child_list' => [
                    [
                        'name' => 'SHOP_DIY_MANAGEMENT',
                        'title' => '商城首页',
                        'url' => 'shop/diy/management',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'SHOP_DIY_INDEX',
                                'title' => '主页装修',
                                'url' => 'shop/diy/index',
                                'is_show' => 0,
                            ],
                        ],
                    ],
                    [
                        'name' => 'SHOP_DIY_GOODS_CATEGORY',
                        'title' => '分类页面',
                        'url' => 'shop/diy/goodscategory',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                    ],
                    [
                        'name' => 'SHOP_DIY_MEMBER_INDEX',
                        'title' => '会员中心',
                        'url' => 'shop/diy/memberindex',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                    ],
                    [
                        'name' => 'SHOP_DIY_GOODS_DETAIL_CONFIG',
                        'title' => '商品详情',
                        'url' => 'shop/goods/goodsdetailconfig',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 4,
                    ],
                    [
                        'name' => 'SHOP_DIY_PROMOTION_ZONE_CONFIG',
                        'title' => '活动专区',
                        'url' => 'shop/promotion/zoneconfig',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 5,
                    ],
                    [
                        'name' => 'SHOP_DIY_LISTS',
                        'title' => '微页面',
                        'url' => 'shop/diy/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 6,
                        'child_list' => [
                            [
                                'name' => 'SHOP_DIY_EDIT',
                                'title' => '编辑自定义页面',
                                'url' => 'shop/diy/edit',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'SHOP_DIY_DELETE',
                                'title' => '删除自定义页面',
                                'url' => 'shop/diy/deleteSiteDiyView',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'SHOP_DIY_SET_USE',
                                'title' => '设为使用',
                                'url' => 'shop/diy/setUse',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],
                    [
                        'name' => 'SHOP_DIY_ROUTE',
                        'title' => '页面路径',
                        'url' => 'shop/diy/route',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 7,
                    ],
                    [
                        'name' => 'SHOP_DIY_BOTTOM_NAV',
                        'title' => '底部导航',
                        'url' => 'shop/diy/bottomnavdesign',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 8,
                    ],
                    [
                        'name' => 'SHOP_STYLE_CONFIG',
                        'title' => '商城风格',
                        'url' => 'shop/diy/style',
                        'is_show' => 0,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 9,
                    ],
                    [
                        'name' => 'SHOP_STYLE_TEMPLATE',
                        'title' => '模板选择',
                        'url' => 'shop/diy/template',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 10,
                        'child_list' => [
                            [
                                'name' => 'SHOP_STYLE_TEMPLATE_EDIT',
                                'title' => '模板编辑',
                                'url' => 'shop/diy/create',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],
                    [
                        'name' => 'MOBILE_ADV',
                        'title' => '广告管理',
                        'url' => 'shop/adv/index',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 11,
                        'child_list' => [
                            [
                                'name' => 'MOBILE_ADV_POSITION',
                                'title' => '广告位管理',
                                'url' => 'shop/adv/index',
                                'is_show' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'MOBILE_ADV_POSITION_ADD',
                                        'title' => '添加广告位',
                                        'url' => 'shop/adv/addposition',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MOBILE_ADV_POSITION_EDIT',
                                        'title' => '编辑广告位',
                                        'url' => 'shop/adv/editposition',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MOBILE_ADV_POSITION_DELETE',
                                        'title' => '删除广告位',
                                        'url' => 'shop/adv/deleteposition',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MOBILE_ADV_POSITION_STATE_ALTER',
                                        'title' => '更改状态',
                                        'url' => 'shop/adv/alteradvpositionstate',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'MOBILE_ADV_LISTS',
                                'title' => '广告管理',
                                'url' => 'shop/adv/lists',
                                'is_show' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'MOBILE_ADV_ADD',
                                        'title' => '添加广告',
                                        'url' => 'shop/adv/addadv',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MOBILE_ADV_EDIT',
                                        'title' => '编辑广告',
                                        'url' => 'shop/adv/editadv',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MOBILE_ADV_DELETE',
                                        'title' => '删除广告',
                                        'url' => 'shop/adv/deleteadv',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MOBILE_ADV_STATE_ALTER',
                                        'title' => '更改状态',
                                        'url' => 'shop/adv/alteradvstate',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                ]
                            ]
                        ]
                    ],
                    [
                        'name' => 'GOODS_GUESS_YOU_LIKE',
                        'title' => '商品推荐',
                        'url' => 'shop/goods/guessyoulike',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 12,
                        'picture' => '',
                        'picture_selected' => '',
                        'child_list' => []
                    ],
                    [
                        'name' => 'GOODS_LIST_CONFIG',
                        'title' => '商品列表',
                        'url' => 'shop/goods/goodslistconfig',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 13,
                        'picture' => '',
                        'picture_selected' => '',
                        'child_list' => []
                    ],
                ]
            ],
            [
                'name' => 'WEBSITE_CONFIG',
                'title' => '内容管理',
                'url' => 'shop/help/helplist',
                'parent' => '',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/shop_content.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/shop_content_select.png',
                'sort' => 8,
                'child_list' => [
                    [
                        'name' => 'WEBSITE_HELP_MANAGE',
                        'title' => '店铺帮助',
                        'url' => 'shop/help/helplist',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'WEBSITE_HELP',
                                'title' => '帮助列表',
                                'url' => 'shop/help/helplist',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'WEBSITE_HELP_ADD',
                                        'title' => '添加帮助',
                                        'url' => 'shop/help/addhelp',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'WEBSITE_HELP_EDIT',
                                        'title' => '编辑帮助',
                                        'url' => 'shop/help/edithelp',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'WEBSITE_HELP_DELETE',
                                        'title' => '删除帮助',
                                        'url' => 'shop/help/deletehelp',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'WEBSITE_HELP_MODIFY_SORT',
                                        'title' => '帮助排序',
                                        'url' => 'shop/help/modifySort',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'WEBSITE_HELP_CLASS',
                                'title' => '帮助分类',
                                'url' => 'shop/help/classlist',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'child_list' => [
                                    [
                                        'name' => 'WEBSITE_HELP_CLASS_ADD',
                                        'title' => '添加分类',
                                        'url' => 'shop/help/addclass',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'WEBSITE_HELP_CLASS_EDIT',
                                        'title' => '编辑分类',
                                        'url' => 'shop/help/editclass',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'WEBSITE_HELP_CLASS_DELETE',
                                        'title' => '删除分类',
                                        'url' => 'shop/help/deleteclass',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'WEBSITE_HELP_CLASS_MODIFY_SORT',
                                        'title' => '分类排序',
                                        'url' => 'shop/help/modifyClassSort',
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                        ],
                    ],
                    [
                        'name' => 'WEBSITE_NOTICE',
                        'title' => '店铺公告',
                        'url' => 'shop/notice/index',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                        'child_list' => [
                            [
                                'name' => 'WEBSITE_NOTICE_ADD',
                                'title' => '添加公告',
                                'url' => 'shop/notice/addnotice',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'WEBSITE_NOTICE_EDIT',
                                'title' => '编辑公告',
                                'url' => 'shop/notice/editnotice',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'WEBSITE_NOTICE_DELETE',
                                'title' => '删除公告',
                                'url' => 'shop/notice/deletenotice',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'WEBSITE_NOTICE_TOP',
                                'title' => '公告置顶',
                                'url' => 'shop/notice/modifynoticetop',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'WEBSITE_NOTICE_DETAIL',
                                'title' => '公告详情',
                                'url' => 'shop/notice/detail',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],
                    [
                        'name' => 'ALBUM_MANAGE',
                        'title' => '素材管理',
                        'url' => 'shop/album/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 3,
                        'picture' => 'app/shop/view/public/img/icon_new/picture_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/picture_select.png',
                        'child_list' => [
                            [
                                'name' => 'ALBUM_ADD',
                                'title' => '添加素材分组',
                                'url' => 'shop/album/addalbum',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ALBUM_EDIT',
                                'title' => '编辑素材分组',
                                'url' => 'shop/album/editalbum',
                                'sort' => 2,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ALBUM_DELETE',
                                'title' => '删除素材分组',
                                'url' => 'shop/album/deletealbum',
                                'sort' => 3,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ALBUM_PIC_MODIFY_PICNAME',
                                'title' => '编辑文件名称',
                                'url' => 'shop/album/modifypicname',
                                'sort' => 4,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ALBUM_PIC_MODIFY_ALBUM',
                                'title' => '修改文件分组',
                                'url' => 'shop/album/modifyfilealbum',
                                'sort' => 5,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ALBUM_PIC_DELETE',
                                'title' => '删除文件',
                                'url' => 'shop/album/deletefile',
                                'sort' => 6,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ALBUM_BOX',
                                'title' => '素材',
                                'url' => 'shop/album/album',
                                'sort' => 7,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'ARTICLE_MANAGE',
                        'title' => '文章管理',
                        'url' => 'shop/article/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 3,
                        'picture' => '',
                        'picture_selected' => '',
                        'child_list' => [
                            [
                                'name' => 'ARTICLE_LIST',
                                'title' => '文章列表',
                                'url' => 'shop/article/lists',
                                'sort' => 1,
                                'is_show' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'ARTICLE_ADD',
                                        'title' => '添加文章',
                                        'url' => 'shop/article/add',
                                        'sort' => 1,
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'ARTICLE_EDIT',
                                        'title' => '编辑文章',
                                        'url' => 'shop/article/edit',
                                        'sort' => 2,
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'ARTICLE_DELETE',
                                        'title' => '删除文章',
                                        'url' => 'shop/article/delete',
                                        'sort' => 3,
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'ARTICLE_SORT',
                                        'title' => '文章排序',
                                        'url' => 'shop/article/modifysort',
                                        'sort' => 4,
                                        'is_show' => 0,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'ARTICLE_DRAFTS_LIST',
                                'title' => '草稿箱',
                                'url' => 'shop/article/drafts',
                                'sort' => 2,
                                'is_show' => 1
                            ],
                            [
                                'name' => 'ARTICLE_CATEGORY_LIST',
                                'title' => '文章分类',
                                'url' => 'shop/articlecategory/lists',
                                'sort' => 3,
                                'is_show' => 1
                            ],
                        ]
                    ],
                ]
            ]
        ]
    ],
    [
        'name' => 'GOODS_ROOT',
        'title' => '商品',
        'url' => 'shop/goods/lists',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconshangpin',
        'picture_selected' => '',
        'sort' => 4,
        'child_list' => [
            [
                'name' => 'GOODS_MANAGE',
                'title' => '商品管理',
                'url' => 'shop/goods/lists',
                'is_show' => 1,
                'is_control' => 1,
                'sort' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/goods_list_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/goods_list_select.png',
                'child_list' => [
                    [
                        'name' => 'GOODS_LIST',
                        'title' => '商品列表',
                        'url' => 'shop/goods/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'sort' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/goods_list_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/goods_list_select.png',
                        'child_list' => [
                            [
                                'name' => 'PHYSICAL_GOODS_ADD',
                                'title' => '发布实物商品',
                                'url' => 'shop/goods/addgoods',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'PHYSICAL_GOODS_EDIT',
                                'title' => '编辑实物商品',
                                'url' => 'shop/goods/editgoods',
                                'sort' => 2,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'VIRTUAL_GOODS_ADD',
                                'title' => '发布虚拟商品',
                                'url' => 'shop/virtualgoods/addgoods',
                                'sort' => 3,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'VIRTUAL_GOODS_EDIT',
                                'title' => '编辑虚拟商品',
                                'url' => 'shop/virtualgoods/editgoods',
                                'sort' => 4,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_OFF',
                                'title' => '商品下架',
                                'url' => 'shop/goods/offgoods',
                                'sort' => 5,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ON',
                                'title' => '商品上架',
                                'url' => 'shop/goods/ongoods',
                                'sort' => 6,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_DELETE',
                                'title' => '商品删除',
                                'url' => 'shop/goods/deletegoods',
                                'sort' => 7,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_EDIT_STOCK',
                                'title' => '编辑商品库存',
                                'url' => 'shop/goods/editGoodsStock',
                                'sort' => 8,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_COPY',
                                'title' => '复制商品',
                                'url' => 'shop/goods/copyGoods',
                                'sort' => 9,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_MODIFY_SORT',
                                'title' => '商品排序',
                                'url' => 'shop/goods/modifySort',
                                'sort' => 10,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_BROWSE',
                                'title' => '浏览记录',
                                'url' => 'shop/goods/goodsbrowse',
                                'sort' => 11,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_MODIFY_BATCHSET',
                                'title' => '批量设置',
                                'url' => 'shop/goods/batchset',
                                'sort' => 12,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_VERIFY_LIST',
                                'title' => '商品核销',
                                'url' => 'shop/goods/verify',
                                'is_show' => 0,
                                'sort' => 15,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_EXPORT_LIST',
                                'title' => '商品导出记录',
                                'url' => 'shop/goods/export',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 16,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_STOCK_TRANSFORM',
                                'title' => '库存转换',
                                'url' => 'shop/goods/stocktransform',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'sort' => 17,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'GOODS_CATEGORY',
                        'title' => '商品分类',
                        'url' => 'shop/goodscategory/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 2,
                        'picture' => 'app/shop/view/public/img/icon_new/category_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/category_select.png',
                        'child_list' => [
                            [
                                'name' => 'GOODS_CATEGORY_ADD',
                                'title' => '商品分类添加',
                                'url' => 'shop/goodscategory/addcategory',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_CATEGORY_EDIT',
                                'title' => '商品分类编辑',
                                'url' => 'shop/goodscategory/editcategory',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_CATEGORY_DELETE',
                                'title' => '商品分类删除',
                                'url' => 'shop/goodscategory/deletecategory',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_CATEGORY_MODIFY_SORT',
                                'title' => '商品分类排序',
                                'url' => 'shop/goodscategory/modifySort',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'GOODS_BRAND_MANAGE',
                        'title' => '商品品牌',
                        'url' => 'shop/goodsbrand/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'GOODS_BRAND_ADD',
                                'title' => '品牌添加',
                                'url' => 'shop/goodsbrand/addbrand',
                                'sort' => 3,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_BRAND_EDIT',
                                'title' => '品牌编辑',
                                'url' => 'shop/goodsbrand/editbrand',
                                'sort' => 4,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_BRAND_DEL',
                                'title' => '品牌删除',
                                'url' => 'shop/goodsbrand/deletebrand',
                                'sort' => 5,
                                'is_show' => 0,
                                'type' => 'button',
                            ]

                        ]
                    ],
                    [
                        'name' => 'GOODS_LABEL',
                        'title' => '商品标签',
                        'url' => 'shop/goodslabel/lists',
                        'is_show' => 1,
                        'sort' => 4,
                        'picture' => 'app/shop/view/public/img/icon_new/goods_label_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/goods_label_select.png',
                        'child_list' => [
                            [
                                'name' => 'GOODS_LABEL_ADD',
                                'title' => '添加商品标签',
                                'url' => 'shop/goodslabel/add',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_LABEL_EDIT',
                                'title' => '编辑商品标签',
                                'url' => 'shop/goodslabel/edit',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_LABEL_DEL',
                                'title' => '商品标签删除',
                                'url' => 'shop/goodslabel/delete',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_LABEL_MODIFY_SORT',
                                'title' => '商品标签排序',
                                'url' => 'shop/goodslabel/modifySort',
                                'is_show' => 0,
                                'type' => 'button',
                            ]
                        ]
                    ],
                    [
                        'name' => 'GOODS_ATTR',
                        'title' => '商品参数',
                        'url' => 'shop/goodsattr/lists',
                        'is_show' => 1,
                        'sort' => 5,
                        'picture' => 'app/shop/view/public/img/icon_new/goods_property_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/goods_property_select.png',
                        'child_list' => [
                            [
                                'name' => 'GOODS_ATTR_ADD',
                                'title' => '添加参数类型',
                                'url' => 'shop/goodsattr/addAttr',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_EDIT',
                                'title' => '编辑参数类型',
                                'url' => 'shop/goodsattr/editattr',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_DEL',
                                'title' => '删除参数类型',
                                'url' => 'shop/goodsattr/deleteattr',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_MODIFY_SORT',
                                'title' => '参数类型排序',
                                'url' => 'shop/goodsattr/modifySort',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_ADD',
                                'title' => '添加参数',
                                'url' => 'shop/goodsattr/addattribute',
                                'is_show' => 0
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_EDIT',
                                'title' => '编辑参数',
                                'url' => 'shop/goodsattr/editAttribute',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_DELETE',
                                'title' => '删除参数',
                                'url' => 'shop/goodsattr/deleteAttribute',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_VALUE_ADD',
                                'title' => '添加参数值',
                                'url' => 'shop/goodsattr/addAttributeValue',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_VALUE_EDIT',
                                'title' => '编辑参数值',
                                'url' => 'shop/goodsattr/editAttributeValue',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_VALUE_DELETE',
                                'title' => '删除参数值',
                                'url' => 'shop/goodsattr/deleteAttributeValue',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_ATTR_ATTRIBUTE_VALUE_MODIFY_SORT',
                                'title' => '参数排序',
                                'url' => 'shop/goodsattr/modifyAttributeSort',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'GOODS_SERVICE',
                        'title' => '商品服务',
                        'url' => 'shop/goodsservice/lists',
                        'is_show' => 1,
                        'sort' => 6,
                        'picture' => 'app/shop/view/public/img/icon_new/goods_serve_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/goods_serve_select.png',
                        'child_list' => [
                            [
                                'name' => 'GOODS_SERVICE_ADD',
                                'title' => '添加商品服务',
                                'url' => 'shop/goodsservice/add',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_SERVICE_EDIT',
                                'title' => '编辑商品服务',
                                'url' => 'shop/goodsservice/edit',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_SERVICE_DEL',
                                'title' => '商品服务删除',
                                'url' => 'shop/goodsservice/delete',
                                'is_show' => 0,
                                'type' => 'button',
                            ]
                        ]
                    ],
                    [
                        'name' => 'PHYSICAL_GOODS_RECYCLE',
                        'title' => '回收站',
                        'url' => 'shop/goods/recycle',
                        'is_show' => 1,
                        'is_control' => 1,
                        'sort' => 11,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/recycle_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/recycle_select.png',
                        'child_list' => [
                            [
                                'name' => 'PHYSICAL_GOODS_RECYCLE_DELETE',
                                'title' => '回收站删除',
                                'url' => 'shop/goods/deleteRecycleGoods',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'PHYSICAL_GOODS_RECYCLE_RECOVERY',
                                'title' => '回收站恢复',
                                'url' => 'shop/goods/recoveryrecycle',
                                'sort' => 2,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                ]
            ],
            [
                'name' => 'GOODS_TOOL',
                'title' => '商品工具',
                'url' => 'shop/goods/import',
                'is_show' => 1,
                'is_control' => 1,
                'sort' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/goods_list_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/goods_list_select.png',
                'child_list' => [
                    [
                        'name' => 'PHYSICAL_GOODS_IMPORT',
                        'title' => '商品导入',
                        'url' => 'shop/goods/import',
                        'is_show' => 1,
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'PHYSICAL_GOODS_IMPORT_RECORD_LIST',
                                'title' => '商品导入历史',
                                'url' => 'shop/goods/importRecordList',
                                'is_show' => 0,
                                'sort' => 14,
                                'type' => 'button',
                            ],
                        ]

                    ],

                ]
            ]

        ]
    ],
    [
        'name' => 'ORDER_ROOT',
        'title' => '订单',
        'url' => 'shop/order/lists',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'icondingdan',
        'picture_selected' => '',
        'sort' => 5,
        'child_list' => [
            [
                'name' => 'ORDER_MANAGE',
                'title' => '订单管理',
                'url' => 'shop/order/lists',
                'parent' => '',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/order_management_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/order_management_select.png',
                'sort' => 1,
                'child_list' => [
                    [
                        'name' => 'EXPRESS_ORDER_LIST',
                        'title' => '商城订单',
                        'url' => 'shop/order/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'EXPRESS_ORDER_DETAIL',
                                'title' => '订单详情',
                                'url' => 'shop/order/detail',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_EXPORT_LIST',
                                'title' => '订单导出记录',
                                'url' => 'shop/order/export',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'EXPRESS_ORDER_CLOSE',
                                'title' => '订单关闭',
                                'url' => 'shop/order/close',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'EXPRESS_ORDER_ADJUST_PRICE',
                                'title' => '订单调价',
                                'url' => 'shop/order/adjustprice',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REMARK',
                                'title' => '订单备注',
                                'url' => 'shop/order/orderRemark',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_DELETE',
                                'title' => '订单删除',
                                'url' => 'shop/order/delete',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'EXPRESS_ORDER_EDIT_ADDRESS',
                                'title' => '订单修改收货地址',
                                'url' => 'shop/order/editaddress',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'LOCAL_ORDER_DETAIL',
                                'title' => '外卖订单详情',
                                'url' => 'shop/localorder/detail',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'LOCAL_ORDER_DELIVER',
                                'title' => '外卖订单发货',
                                'url' => 'shop/localorder/delivery',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'STORE_ORDER_DETAIL',
                                'title' => '自提订单详情',
                                'url' => 'shop/storeorder/detail',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'VIRTUAL_ORDER_DETAIL',
                                'title' => '虚拟订单详情',
                                'url' => 'shop/virtualorder/detail',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_TAKE_DELIVERY',
                                'title' => '确认收货',
                                'url' => 'shop/order/takeDelivery',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_DELIVERY',
                                'title' => '发货',
                                'url' => 'shop/order/delivery',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_BATCH_DELIVERY',
                                'title' => '批量发货',
                                'url' => 'shop/delivery/batchdelivery',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'ORDER_REFUND_LIST',
                        'title' => '退款维权',
                        'url' => 'shop/orderrefund/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/refund_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/refund_select.png',
                        'sort' => 20,
                        'child_list' => [
                            [
                                'name' => 'ORDER_REFUND_DETAIL',
                                'title' => '维权详情',
                                'url' => 'shop/orderrefund/detail',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REFUND_REFUSE',
                                'title' => '维权拒绝',
                                'url' => 'shop/orderrefund/refuse',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REFUND_AGREE',
                                'title' => '维权同意',
                                'url' => 'shop/orderrefund/agree',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REFUND_AGREE',
                                'title' => '维权收货',
                                'url' => 'shop/orderrefund/receive',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REFUND_COMPLETE',
                                'title' => '维权通过',
                                'url' => 'shop/orderrefund/complete',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REFUND_EXPORT_LIST',
                                'title' => '订单维权导出记录',
                                'url' => 'shop/orderrefund/export',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_REFUND_CLOSE',
                                'title' => '关闭维权',
                                'url' => 'shop/orderrefund/close',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                        ]
                    ],
                ],
            ],
            [
                'name' => 'ORDER_ACTION',
                'title' => '订单处理',
                'url' => 'shop/order/pickuporder',
                'is_show' => 1,
                'parent' => '',
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon/pickuporder.png',
                'picture_selected' => 'app/shop/view/public/img/icon/selectedpickuporder.png',
                'sort' => 2,
                'child_list' => [
                    [
                        'name' => 'EXPRESS_ORDER_PICK_UP_ORDER',
                        'title' => '订单自提',
                        'url' => 'shop/order/pickuporder',
                        'is_show' => 1,
                        'parent' => '',
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon/pickuporder.png',
                        'picture_selected' => 'app/shop/view/public/img/icon/selectedpickuporder.png',
                        'sort' => 1,
                        'child_list' => [
                        ]
                    ],
                    [
                        'name' => 'ORDER_DELIVERY_LIST',
                        'title' => '订单发货',
                        'url' => 'shop/delivery/lists',
                        'parent' => '',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/deliver_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/deliver_select.png',
                        'sort' => 2,
                        'child_list' => [
                            [
                                'name' => 'ORDER_DELIVERY_EXPRESS_ELECTRONICSHEETLIST',
                                'title' => '获取电子面单模板列表',
                                'url' => 'shop/delivery/getexpresselectronicsheetlist',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_DELIVERY_EXPRESS_PRINT_ELECTRONICSHEET',
                                'title' => '打印电子面单',
                                'url' => 'shop/delivery/printElectronicsheet',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_DELIVERY_EDIT_ORDER_DELIVERY',
                                'title' => '修改订单物流信息',
                                'url' => 'shop/delivery/editOrderDelivery',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'ORDER_IMPORT_FILE',
                        'title' => '批量发货',
                        'url' => 'shop/orderimportfile/lists',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/batch_deliver_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/batch_deliver_select.png',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'ORDER_IMPORT_FILE_DETAIL',
                                'title' => '详情',
                                'url' => 'shop/orderimportfile/detail',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'ORDER_VERIFY',
                        'title' => '订单核销',
                        'url' => 'shop/verify/orderverify',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/verify_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/verify_select.png',
                        'sort' => 4,
                        'child_list' => [
                            [
                                'name' => 'ORDER_VERIFY_CARD',
                                'title' => '核销台',
                                'url' => 'shop/verify/verifycard',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_VERIFY_RECORDS',
                                'title' => '核销记录',
                                'url' => 'shop/verify/records',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_VERIFY_CONFIRM',
                                'title' => '核销',
                                'url' => 'shop/verify/verify',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 3,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'ORDER_VERIFY_USER',
                        'title' => '核销人员',
                        'url' => 'shop/verify/user',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 5,
                        'child_list' => [
                            [
                                'name' => 'ORDER_VERIFY_USER_ADD',
                                'title' => '添加核销人员',
                                'url' => 'shop/verify/adduser',
                                'is_show' => 0,
                                'is_control' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ORDER_VERIFY_USER_DELETE',
                                'title' => '删除核销人员',
                                'url' => 'shop/verify/deleteuser',
                                'is_show' => 0,
                                'is_control' => 1,
                                'type' => 'button',
                            ], [
                                'name' => 'ORDER_VERIFY_USER_EDIT',
                                'title' => '编辑核销人员',
                                'url' => 'shop/verify/edituser',
                                'is_show' => 0,
                                'is_control' => 1,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'GOODS_EVALUATE',
                        'title' => '订单评价',
                        'url' => 'shop/goods/evaluate',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'sort' => 7,
                        'picture' => '',
                        'picture_selected' => '',
                        'child_list' => [
                            [
                                'name' => 'GOODS_EVALUATE_DELETE',
                                'title' => '删除评价',
                                'url' => 'shop/goods/deleteevaluate',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_EVALUATE_APPLY',
                                'title' => '评价回复',
                                'url' => 'shop/goods/evaluateapply',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_EVALUATE_DELETE_CONTENT',
                                'title' => '删除评价回复',
                                'url' => 'shop/goods/deleteContent',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'GOODS_EVALUATE_MODIFY_AUDIT',
                                'title' => '评价审核',
                                'url' => 'shop/goods/modifyAuditEvaluate',
                                'sort' => 1,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                ]
            ],
        ]
    ],
    [
        'name' => 'MEMBER_ROOT',
        'title' => '会员',
        'url' => 'shop/member/index',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconhuiyuan1',
        'picture_selected' => '',
        'sort' => 6,
        'child_list' => [
            [
                'name' => 'MEMBER_INDEX',
                'title' => '会员管理',
                'url' => 'shop/member/memberlist',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon_new/member_list_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/member_list_select.png',
                'sort' => 2,
                'child_list' => [
                    [
                        'name' => 'SHOP_MEMBER_INDEX',
                        'title' => '会员概况',
                        'url' => 'shop/member/index',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/member_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/member_select.png',
                        'sort' => 0,
                    ],
                    [
                        'name' => 'MEMBER_LIST',
                        'title' => '会员列表',
                        'url' => 'shop/member/memberlist',
                        'is_show' => 1,
                        'sort' => 2,
                        'child_list' => [
                            [
                                'name' => 'MEMBER_ADD',
                                'title' => '会员添加',
                                'url' => 'shop/member/addmember',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_EDIT',
                                'title' => '基础信息',
                                'url' => 'shop/member/editmember',
                                'is_show' => 0,
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_DELETE',
                                'title' => '会员删除',
                                'url' => 'shop/member/deletemember',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_ACCOUNT_DETAIL',
                                'title' => '账户明细',
                                'url' => 'shop/member/accountdetail',
                                'is_show' => 0,
                                'sort' => 2,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_ORDER',
                                'title' => '订单管理',
                                'url' => 'shop/member/order',
                                'is_show' => 0,
                                'sort' => 3,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_ADDRESS',
                                'title' => '会员地址',
                                'url' => 'shop/member/addressdetail',
                                'is_show' => 0,
                                'sort' => 4,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_DETAIL',
                                'title' => '会员详情',
                                'url' => 'shop/member/memberdetail',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_LABEL_MODIFY',
                                'title' => '修改会员标签',
                                'url' => 'shop/member/modifylabel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_STATUS_MODIFY',
                                'title' => '修改会员状态',
                                'url' => 'shop/member/modifystatus',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_PASSWORD_MODIFY',
                                'title' => '修改会员密码',
                                'url' => 'shop/member/modifypassword',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_BALANCE_ADJUST',
                                'title' => '余额调整（不可提现）',
                                'url' => 'shop/member/adjustbalance',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_BALANCE_ADJUST_BALANCE_MONEY',
                                'title' => '余额调整（可提现）',
                                'url' => 'shop/member/adjustbalancemoney',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_POINT_ADJUST',
                                'title' => '积分调整',
                                'url' => 'shop/member/adjustpoint',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_GROWTH_ADJUST',
                                'title' => '成长值调整',
                                'url' => 'shop/member/adjustgrowth',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_COLLECT',
                                'title' => '收藏记录',
                                'url' => 'shop/goods/membergoodscollect',
                                'is_show' => 0,
                                'sort' => 5,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_BROWSE',
                                'title' => '浏览记录',
                                'url' => 'shop/goods/membergoodsbrowse',
                                'is_show' => 0,
                                'sort' => 6,
                                'type' => 'button',
                            ],

                        ]
                    ],
                    [
                        'name' => 'MEMBER_IMPORT',
                        'title' => '会员导入',
                        'url' => 'shop/member/memberimport',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/member_channel_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/member_channel_select.png',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'MEMBER_IMPORT_LIST',
                                'title' => '导入记录',
                                'url' => 'shop/member/memberimportlist',
                                'is_show' => 0,
                                'type' => 'button',
                            ]
                        ]
                    ],
                    [
                        'name' => 'MEMBER_LABEL',
                        'title' => '会员标签',
                        'url' => 'shop/memberlabel/labellist',
                        'is_show' => 1,
                        'sort' => 10,
                        'picture' => 'app/shop/view/public/img/icon_new/member_label_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/member_label_select.png',
                        'child_list' => [
                            [
                                'name' => 'MEMBER_LABEL_ADD',
                                'title' => '标签添加',
                                'url' => 'shop/memberlabel/addlabel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_LABEL_EDIT',
                                'title' => '标签修改',
                                'url' => 'shop/memberlabel/editlabel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_LABEL_DELETE',
                                'title' => '标签删除',
                                'url' => 'shop/memberlabel/deletelabel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_LABEL_SORT_MODIFY',
                                'title' => '修改排序',
                                'url' => 'shop/memberlabel/modifysort',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'MEMBER_CLUSTER',
                        'title' => '会员群体',
                        'url' => 'shop/membercluster/clusterlist',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 11,
                        'child_list' => [
                            [
                                'name' => 'MEMBER_CLUSTER_ADD',
                                'title' => '群体添加',
                                'url' => 'shop/membercluster/addcluster',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_EDIT',
                                'title' => '群体编辑',
                                'url' => 'shop/membercluster/editcluster',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_DELETE',
                                'title' => '群体删除',
                                'url' => 'shop/membercluster/deletecluster',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_BALANCE_ADJUST',
                                'title' => '发放红包',
                                'url' => 'shop/membercluster/sendbalance',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_POINT_ADJUST',
                                'title' => '发放积分',
                                'url' => 'shop/membercluster/sendpoint',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_COUPON_ADJUST',
                                'title' => '发放优惠券',
                                'url' => 'shop/membercluster/sendcoupon',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_CALCULATE',
                                'title' => '计算群体',
                                'url' => 'shop/membercluster/calculate',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_EXPORT_MEMBER',
                                'title' => '导出会员',
                                'url' => 'shop/membercluster/exportclustermember',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_CLUSTER_REFRESH',
                                'title' => '刷新信息',
                                'url' => 'shop/membercluster/refreshcluster',
                                'is_show' => 0,
                                'type' => 'button',
                            ],

                        ]
                    ],
                ],
            ],
            [
                'name' => 'MEMBER_LEVEL_ROOT',
                'title' => '等级权益',
                'url' => 'shop/memberlevel/levellist',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon_new/member_group_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/member_group_select.png',
                'sort' => 3,
                'child_list' => [
                    [
                        'name' => 'MEMBER_LEVEL',
                        'title' => '会员等级',
                        'url' => 'shop/memberlevel/levellist',
                        'is_show' => 1,
                        'sort' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/member_class_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/member_class_select.png',
                        'child_list' => [
                            [
                                'name' => 'MEMBER_LEVEL_ADD',
                                'title' => '会员等级添加',
                                'url' => 'shop/memberlevel/addlevel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_LEVEL_EDIT',
                                'title' => '会员等级修改',
                                'url' => 'shop/memberlevel/editlevel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_LEVEL_DELETE',
                                'title' => '会员等级删除',
                                'url' => 'shop/memberlevel/deletelevel',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            /*[
                                'name' => 'MEMBER_ACCOUNT_GROWTH',
                                'title' => '会员成长值',
                                'url' => 'shop/memberaccount/growth',
                                'is_show' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 3,
                                'child_list' => [],
                            ],*/

                        ]
                    ],
                ],
            ],
            [
                'name' => 'MEMBER_ACCOUNT_BALANCE',
                'title' => '余额储值',
                'url' => 'shop/memberaccount/balance',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon_new/member_group_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/member_group_select.png',
                'sort' => 3,
                'child_list' => [
                    [
                        'name' => 'MEMBER_ACCOUNT_BALANCE_LIST',
                        'title' => '余额流水',
                        'url' => 'shop/memberaccount/balance',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                        'child_list' => [],
                    ],
                ],
            ],
            [
                'name' => 'MEMBER_ACCOUNT_POINT',
                'title' => '会员积分',
                'url' => 'shop/memberaccount/point',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon_new/member_group_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/member_group_select.png',
                'sort' => 3,
                'child_list' => [
                    [
                        'name' => 'MEMBER_ACCOUNT_POINT_CONFIG',
                        'title' => '积分规则',
                        'url' => 'shop/memberaccount/pointconfig',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'MEMBER_ACCOUNT_POINT_TASK_CONFIG',
                                'title' => '积分任务设置',
                                'url' => 'shop/memberaccount/pointtaskconfig',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_ACCOUNT_POINT_CLEAR',
                                'title' => '积分清零',
                                'url' => 'shop/memberaccount/pointclear',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_ACCOUNT_POINT_RESET',
                                'title' => '积分重置',
                                'url' => 'shop/memberaccount/pointreset',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],
                    [
                        'name' => 'MEMBER_ACCOUNT_POINT_LIST',
                        'title' => '积分流水',
                        'url' => 'shop/memberaccount/point',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                        'child_list' => [],
                    ],
                ],
            ],

        ]
    ],
    [
        'name' => 'PROMOTION_ROOT',
        'title' => '营销',
        'url' => 'shop/promotion/index',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconyingxiaozhongxin',
        'picture_selected' => '',
        'sort' => 7,
        'child_list' => [
            [
                'name' => 'PROMOTION_CENTER',
                'title' => '营销中心',
                'url' => 'shop/promotion/index',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/promotion_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/promotion_select.png',
                'sort' => 1,
                'child_list' => [
                    [
                        'name' => 'PROMOTION_INDEX',
                        'title' => '营销概况',
                        'url' => 'shop/promotion/index',
                        'is_show' => 1,
                        'is_control' => 0,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/promotion_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/promotion_select.png',
                        'sort' => 1,
                    ],
                    [
                        'name' => 'PROMOTION_CENTER_MARKET',
                        'title' => '营销活动',
                        'url' => 'shop/promotion/market',
                        'is_show' => 1,
                        'is_control' => 0,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/promotion_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/promotion_select.png',
                        'sort' => 2,
                    ],
                ]
            ],
            [
                'name' => 'PROMOTION_TOOL',
                'title' => '应用工具',
                'url' => 'shop/promotion/tool',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/promotion_tool_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/promotion_tool_select.png',
                'sort' => 4,
                'child_list' => [
                    [
                        'name' => 'TOOL_LIST',
                        'title' => '应用列表',
                        'url' => 'shop/promotion/tool',
                        'is_show' => 1,
                        'is_control' => 0,
                        'is_icon' => 0,
                        'sort' => 0,
                    ],
                ]
            ],
        ]
    ],
    [
        'name' => 'ACCOUNT_ROOT',
        'title' => '财务',
        'url' => 'shop/account/dashboard',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconcaiwu',
        'picture_selected' => '',
        'sort' => 8,
        'child_list' => [
            [
                'name' => 'ACCOUNT_MANAGE',
                'title' => '财务管理',
                'url' => 'shop/account/dashboard',
                'parent' => '',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/menu_icon/icon9.png',
                'picture_selected' => '',
                'sort' => 8,
                'child_list' => [
                    [
                        'name' => 'ACCOUNT_DASHBOARD',
                        'title' => '财务报表',
                        'url' => 'shop/account/dashboard',
                        'is_show' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/money_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/money_select.png',
                        'sort' => 1,
                        'child_list' => []
                    ],
                    [
                        'name' => 'INVOICE_LIST',
                        'title' => '发票管理',
                        'url' => 'shop/order/invoiceorderlist',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/invoice_management_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/invoice_management_select.png',
                        'sort' => 2,
                        'child_list' => [
                            [
                                'name' => 'INVOICE_EDIT',
                                'title' => '发票编辑',
                                'url' => 'shop/order/invoiceedit',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                        ]
                    ],
                    [
                        'name' => 'MEMBER_WITHDRAW_LIST',
                        'title' => '余额提现',
                        'url' => 'shop/memberwithdraw/lists',
                        'is_show' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/member_withdraw_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/member_withdraw_select.png',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'MEMBER_WITHDRAW_DETAIL',
                                'title' => '提现详情',
                                'url' => 'shop/memberwithdraw/detail',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_WITHDRAW_TRANSFERFINISH',
                                'title' => '手动转账',
                                'url' => 'shop/memberwithdraw/transferfinish',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_WITHDRAW_agree',
                                'title' => '同意转账',
                                'url' => 'shop/memberwithdraw/agree',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MEMBER_WITHDRAW_refuse',
                                'title' => '拒绝转账',
                                'url' => 'shop/memberwithdraw/refuse',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'ONLINE_TRANSFER',
                                'title' => '在线转账',
                                'url' => 'memberwithdraw://shop/withdraw/transfer',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ]
                    ],
                ]
            ],
        ],
    ],
    [
        'name' => 'STAT_ROOT',
        'title' => '数据',
        'url' => 'shop/stat/shop',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconshuju3',
        'picture_selected' => '',
        'sort' => 9,
        'child_list' => [
            [
                'name' => 'STAT_SHOP',
                'title' => '营业数据',
                'url' => 'shop/stat/shop',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/icon_new/stat_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/stat_select.png',
                'sort' => 2,
                'child_list' => [
                    [
                        'name' => 'STAT_SHOP_INDEX',
                        'title' => '数据概况',
                        'url' => 'shop/stat/shop',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/stat_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/stat_select.png',
                        'sort' => 2,
                    ],
                    [
                        'name' => 'STAT_ORDER',
                        'title' => '交易数据',
                        'url' => 'shop/stat/order',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/stat_order_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/stat_order_select.png',
                        'sort' => 4,
                    ],
                    [
                        'name' => 'STAT_MEMBER',
                        'title' => '会员数据',
                        'url' => 'shop/stat/member',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                    ],
                    [
                        'name' => 'STAT_VISIT',
                        'title' => '流量数据',
                        'url' => 'shop/stat/visit',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/stat_icon_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/stat_icon_select.png',
                        'sort' => 5,
                    ],
                    [
                        'name' => 'STAT_GOODS',
                        'title' => '商品数据',
                        'url' => 'shop/stat/goods',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 6,
                    ],
                ]
            ],
        ]
    ],
    [
        'name' => 'CONFIG_ROOT',
        'title' => '设置',
        'url' => 'shop/shop/config',
        'parent' => '',
        'is_show' => 1,
        'is_control' => 1,
        'is_icon' => 0,
        'picture' => 'iconshezhi-xianxing',
        'picture_selected' => '',
        'sort' => 11,
        'child_list' => [
            [
                'name' => 'CONFIG_BASE',
                'title' => '店铺设置',
                'url' => 'shop/shop/config',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon_new/shop_config_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/shop_config_select.png',
                'sort' => 1,
                'child_list' => [
                    [
                        'name' => 'SHOP_CONFIG',
                        'title' => '店铺信息',
                        'url' => 'shop/shop/config',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 1,
                        'child_list' => [
                            [
                                'name' => 'SHOP_BASE_CONFIG',
                                'title' => '基础信息',
                                'url' => 'shop/shop/config',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                            ],
                            [
                                'name' => 'SHOP_CONTACT',
                                'title' => '联系我们',
                                'url' => 'shop/shop/contact',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 3,
                            ],
                            [
                                'name' => 'SITE_ADDRESS',
                                'title' => '商家地址库',
                                'url' => 'shop/siteaddress/siteaddress',
                                'is_show' => 1,
                                'sort' => 4,
                                'is_control' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'SITE_ADDRESS_ADD',
                                        'title' => '添加商家地址库',
                                        'url' => 'shop/siteaddress/addsiteaddress',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'SITE_ADDRESS_EDIT',
                                        'title' => '编辑商家地址库',
                                        'url' => 'shop/siteaddress/editsiteaddress',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'SITE_ADDRESS_DELETE',
                                        'title' => '删除商家地址库',
                                        'url' => 'shop/siteaddress/deletesiteaddress',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'ADDRESS_MANAGE',
                                'title' => '地区管理',
                                'url' => 'shop/address/manage',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 5,
                            ],
                        ]
                    ],
                    [
                        'name' => 'USER_AUTH',
                        'title' => '员工管理',
                        'url' => 'shop/user/user',
                        'parent' => '',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon/account.png',
                        'picture_selected' => 'app/shop/view/public/img/icon/account.png',
                        'sort' => 3,
                        'child_list' => [
                            [
                                'name' => 'USER_LIST',
                                'title' => '员工管理',
                                'url' => 'shop/user/user',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'USER_ADD',
                                        'title' => '添加员工',
                                        'url' => 'shop/user/adduser',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'USER_EDIT',
                                        'title' => '员工编辑',
                                        'url' => 'shop/user/edituser',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'USER_DELETE',
                                        'title' => '员工删除',
                                        'url' => 'shop/user/deleteuser',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'USER_MODIFY_STATUS',
                                        'title' => '调整员工状态',
                                        'url' => 'shop/user/modifyuserstatus',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'USER_GROUP',
                                'title' => '员工角色',
                                'url' => 'shop/user/group',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'child_list' => [
                                    [
                                        'name' => 'USER_GROUP_ADD',
                                        'title' => '添加角色',
                                        'url' => 'shop/user/addgroup',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'USER_GROUP_EDIT',
                                        'title' => '角色编辑',
                                        'url' => 'shop/user/editgroup',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'USER_GROUP_DELETE',
                                        'title' => '角色删除',
                                        'url' => 'shop/user/deletegroup',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'USER_GROUP_MODIFY_STATUS',
                                        'title' => '调整角色状态',
                                        'url' => 'shop/user/modifygroupstatus',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'USER_LOG',
                                'title' => '操作日志',
                                'url' => 'shop/user/userlog',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 4,
                                'child_list' => [
                                    [
                                        'name' => 'USER_LOG_DELETE',
                                        'title' => '删除日志',
                                        'url' => 'shop/user/deleteUserLog',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                        ]
                    ],
                    [
                        'name' => 'CONFIG_BASE_MEMBER',
                        'title' => '注册登录',
                        'url' => 'shop/member/regconfig',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/member_config_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/member_config_select.png',
                        'sort' => 4,
                        'child_list' => [
                            [
                                'name' => 'LOGIN_REG_CONFIG',
                                'title' => '注册设置',
                                'url' => 'shop/member/regconfig',
                                'is_show' => 1,
                                'sort' => 1,
                            ],
                            [
                                'name' => 'CONFIG_API',
                                'title' => '登录秘钥',
                                'url' => 'shop/config/api',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2
                            ],
                            [
                                'name' => 'LOGIN_REG_AGREEMENT',
                                'title' => '用户协议',
                                'url' => 'shop/member/regagreement',
                                'is_show' => 1,
                                'sort' => 3,
                            ],
                            [
                                'name' => 'LOGIN_PRIVACY_AGREEMENT',
                                'title' => '隐私协议',
                                'url' => 'shop/member/privacyagreement',
                                'is_show' => 1,
                                'sort' => 4,
                            ],
                        ]
                    ],
                    [
                        'name' => 'MESSAGE_LISTS',
                        'title' => '消息通知',
                        'url' => 'shop/message/lists',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 5,
                        'child_list' => [
                            [
                                'name' => 'MESSAGE_EMAIL_EDIT',
                                'title' => '编辑邮件模板',
                                'url' => 'shop/message/editEmailMessage',
                                'parent' => '',
                                'is_show' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MESSAGE_SMS_EDIT',
                                'title' => '编辑短信模板',
                                'url' => 'shop/message/editSmsMessage',
                                'parent' => '',
                                'is_show' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'MESSAGE_SHOP_USER',
                                'title' => '商家会员管理',
                                'url' => 'shop/shopacceptmessage/lists',
                                'parent' => '',
                                'is_show' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 3,
                                'child_list' => [
                                    [
                                        'name' => 'MESSAGE_SHOP_USER_ADD',
                                        'title' => '添加商家消息接收会员',
                                        'url' => 'shop/Shopacceptmessage/add',
                                        'parent' => '',
                                        'is_show' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 4,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'MESSAGE_SHOP_USER_DELETE',
                                        'title' => '删除商家消息接收会员',
                                        'url' => 'shop/Shopacceptmessage/delete',
                                        'parent' => '',
                                        'is_show' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 5,
                                        'type' => 'button',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    [
                        'name' => 'SMS_MANAGE',
                        'title' => '短信中心',
                        'url' => 'shop/message/sms',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 6,
                        'child_list' => [
                            [
                                'name' => 'SMS_LIST',
                                'title' => '短信配置',
                                'url' => 'shop/message/sms',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [],
                            ],
                            [
                                'name' => 'SMS_RECORDS',
                                'title' => '发送记录',
                                'url' => 'shop/message/smsrecords',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [],
                            ],
                        ],
                    ],
                    [
                        'name' => 'CONFIG_BASE_ORDER',
                        'title' => '交易设置',
                        'url' => 'shop/order/config',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/deal_config_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/deal_config_select.png',
                        'sort' => 7,
                        'child_list' => [
                            [
                                'name' => 'ORDER_CONFIG_SETTING',
                                'title' => '订单设置',
                                'url' => 'shop/order/config',
                                'is_show' => 1,
                                'sort' => 1,
                            ],
                            [
                                'name' => 'MEMBER_WITHDRAW_CONFIG',
                                'title' => '提现设置',
                                'url' => 'shop/memberwithdraw/config',
                                'is_show' => 1,
                                'sort' => 3,
                            ],

                        ],
                    ],
                    [
                        'name' => 'CONFIG_PAY',
                        'title' => '支付设置',
                        'url' => 'shop/config/pay',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 8,
                    ],
                    [
                        'name' => 'CONFIG_SERVICER',
                        'title' => '客服设置',
                        'url' => 'shop/config/servicer',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 9,
                    ],
                    [
                        'name' => 'CONFIG_EXPRESS_ROOT',
                        'title' => '配送设置',
                        'url' => 'shop/delivery/express',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/distribution_config_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/distribution_config_select.png',
                        'sort' => 10,
                        'child_list' => [
                            [
                                'name' => 'EXPRESS_STORE_STATUS',
                                'title' => '自提开关',
                                'url' => 'shop/delivery/modifystorestatus',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'EXPRESS_EXPRESS_STATUS',
                                'title' => '物流开关',
                                'url' => 'shop/delivery/modifyexpressstatus',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'EXPRESS_LOCAL_STATUS',
                                'title' => '同城配送开关',
                                'url' => 'shop/delivery/modifylocalstatus',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'EXPRESS_LOCALDELIVERY_CONFIG',
                                'title' => '同城配送',
                                'url' => 'shop/local/local',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                            ],
                            [
                                'name' => 'EXPRESS_LOCALDELIVERY_DELIVER_LISTS',
                                'title' => '配送员列表',
                                'url' => 'shop/local/deliverlists',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'EXPRESS_LOCALDELIVERY_ADD_DELIVER',
                                        'title' => '添加配送员',
                                        'url' => 'shop/local/adddeliver',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'EXPRESS_LOCALDELIVERY_EDIT_DELIVER',
                                        'title' => '编辑配送员',
                                        'url' => 'shop/local/editdeliver',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'EXPRESS_LOCALDELIVERY_DELETE_DELIVER',
                                        'title' => '删除配送员',
                                        'url' => 'shop/local/deletedeliver',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                ],
                            ],
                            [
                                'name' => 'EXPRESS_EDIT_PRINT_TEMPLATE',
                                'title' => '运单模板',
                                'url' => 'shop/express/editprinttemplate',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                            ],
                            [
                                'name' => 'EXPRESS_TEMPLATE',
                                'title' => '运费模板',
                                'url' => 'shop/express/template',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'EXPRESS_TEMPLATE_ADD',
                                        'title' => '添加运费模板',
                                        'url' => 'shop/express/addtemplate',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'EXPRESS_TEMPLATE_EDIT',
                                        'title' => '编辑运费模板',
                                        'url' => 'shop/express/edittemplate',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'EXPRESS_TEMPLATE_DELETE',
                                        'title' => '删除运费模板',
                                        'url' => 'shop/express/deletetemplate',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'EXPRESS_DEFAULT_TEMPLATE',
                                        'title' => '设置默认运费模板',
                                        'url' => 'shop/express/defaultTemplate',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'EXPRESS_COMPANY',
                                'title' => '物流公司',
                                'url' => 'shop/express/expresscompany',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'child_list' => [
                                    [
                                        'name' => 'DELIVERY_EXPRESS_ADD',
                                        'title' => '添加物流公司',
                                        'url' => 'shop/express/addcompany',
                                        'is_show' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'DELIVERY_EXPRESS_EDIT',
                                        'title' => '编辑物流公司',
                                        'url' => 'shop/express/editcompany',
                                        'is_show' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'DELIVERY_EXPRESS_DELETE',
                                        'title' => '删除物流公司',
                                        'url' => 'shop/express/deletecompany',
                                        'is_show' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'type' => 'button',
                                    ],
                                ]
                            ],
                            [
                                'name' => 'EXPRESS_EXPRESS_CONFIG',
                                'title' => '物流跟踪',
                                'url' => 'shop/express/trace',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                            ],
                            [
                                'name' => 'SHOP_STORE_LIST',
                                'title' => '自提点管理',
                                'url' => 'shop/store/lists',
                                'is_show' => 0,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'child_list' => [
                                    [
                                        'name' => 'SHOP_STORE_ADD',
                                        'title' => '添加自提点',
                                        'url' => 'shop/store/addstore',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'SHOP_STORE_EDIT',
                                        'title' => '修改自提点',
                                        'url' => 'shop/store/editstore',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'SHOP_STORE_DELETE',
                                        'title' => '删除自提点',
                                        'url' => 'shop/store/deletestore',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                    [
                                        'name' => 'SHOP_STORE_FROZEN',
                                        'title' => '关闭自提点',
                                        'url' => 'shop/store/frozenStore',
                                        'is_show' => 0,
                                        'is_control' => 1,
                                        'is_icon' => 0,
                                        'picture' => '',
                                        'picture_selected' => '',
                                        'sort' => 1,
                                        'type' => 'button',
                                    ],
                                ],
                            ],
                        ]
                    ],
                    [
                        'name' => 'GOODS_BASIC_CONFIG',
                        'title' => '商品设置',
                        'url' => 'shop/goods/defaultsearchwords',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/goods_config_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/goods_config_select.png',
                        'sort' => 12,
                        'child_list' => [
                            [
                                'name' => 'GOODS_DEFAULT_SEARCH_WORDS',
                                'title' => '商品搜索',
                                'url' => 'shop/goods/defaultsearchwords',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'sort' => 1,
                                'picture' => 'app/shop/view/public/img/icon/default_search.png',
                                'picture_selected' => 'app/shop/view/public/img/icon/sys_config.png',
                                'child_list' => []
                            ],
                            [
                                'name' => 'GOODS_SORT',
                                'title' => '商品排序',
                                'url' => 'shop/goods/goodssort',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'sort' => 2,
                                'picture' => 'app/shop/view/public/img/icon/goods_sort.png',
                                'picture_selected' => 'app/shop/view/public/img/icon/goods_sort.png',
                                'child_list' => []
                            ],
                            [
                                'name' => 'GOODS_NO',
                                'title' => '商品编码',
                                'url' => 'shop/goods/goodsno',
                                'is_show' => 1,
                                'is_control' => 1,
                                'sort' => 3,
                            ],
                        ]
                    ],
                    [
                        'name' => 'CONFIG_BASE_OTHER',
                        'title' => '其他设置',
                        'url' => 'shop/config/defaultpicture',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/goods_config_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/goods_config_select.png',
                        'sort' => 13,
                        'child_list' => [
                            [
                                'name' => 'CONFIG_DEFAULT_PICTURE',
                                'title' => '默认图片',
                                'url' => 'shop/config/defaultpicture',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                            ],
                            [
                                'name' => 'MAP_CONFIG',
                                'title' => '地图配置',
                                'url' => 'shop/config/map',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2
                            ],
                            [
                                'name' => 'CONFIG_CAPTCHA',
                                'title' => '验证码设置',
                                'url' => 'shop/config/captcha',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 3
                            ],
                            [
                                'name' => 'GOODS_AFTERSALE',
                                'title' => '售后保障',
                                'url' => 'shop/config/aftersale',
                                'is_show' => 1,
                                'is_control' => 1,
                                'is_icon' => 0,
                                'sort' => 6,
                                'picture' => 'app/shop/view/public/img/icon_new/after_sales_support_new.png',
                                'picture_selected' => 'app/shop/view/public/img/icon_new/after_sales_support_select.png',
                                'child_list' => []
                            ],
                            [
                                'name' => 'TRANSACTION_AGREEMENT',
                                'title' => '购物须知',
                                'url' => 'shop/order/transactionagreement',
                                'is_show' => 1,
                                'sort' => 7,
                            ],
                            [
                                'name' => 'CONFIG_UPLOAD_SET',
                                'title' => '上传设置',
                                'url' => 'shop/upload/config',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 9,
                            ],
                            [
                                'name' => 'UPLOAD_OSS',
                                'title' => '云上传',
                                'url' => 'shop/upload/oss',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 10,
                            ],
                            [
                                'name' => 'UPLOAD_ALBUM',
                                'title' => '上传素材图片',
                                'url' => 'shop/upload/album',
                                'sort' => 11,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_DOWNLOAD_IMAGE',
                                'title' => '下载图片',
                                'url' => 'shop/upload/download',
                                'sort' => 12,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_IMAGE',
                                'title' => '上传图片',
                                'url' => 'shop/upload/upload',
                                'sort' => 13,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_VIDEO',
                                'title' => '上传视频',
                                'url' => 'shop/upload/video',
                                'sort' => 14,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_VIDEO_TO_ALBUM',
                                'title' => '上传视频到素材',
                                'url' => 'shop/upload/videoToAlbum',
                                'sort' => 15,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_FILE',
                                'title' => '上传文件',
                                'url' => 'shop/upload/file',
                                'sort' => 16,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_CHECK_FILE',
                                'title' => '域名校验文件',
                                'url' => 'shop/upload/checkfile',
                                'sort' => 17,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_MODIFY_IMAGE_FILE',
                                'title' => '替换图片文件',
                                'url' => 'shop/upload/modifyFile',
                                'sort' => 18,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'UPLOAD_MODIFY_VIDEO_FILE',
                                'title' => '替换视频文件',
                                'url' => 'shop/upload/modifyVideoFile',
                                'sort' => 19,
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            // 新增一个 二开设置
                        ]
                    ],
                ]
            ],
            [
                'name' => 'CHANNEL_ROOT',
                'title' => '应用渠道',
                'url' => 'wechat://shop/wechat/setting',
                'parent' => '',
                'is_show' => 1,
                'is_control' => 1,
                'is_icon' => 0,
                'picture' => 'app/shop/view/public/img/menu_icon/icon11.png',
                'picture_selected' => 'app/shop/view/public/img/menu_icon/menu_set_selected.png',
                'sort' => 2,
                'child_list' => []
            ],
            [
                'name' => 'UPGRADE_ROOT',
                'title' => '系统维护',
                'url' => 'shop/config/sitedeploy',
                'is_show' => 1,
                'picture' => 'app/shop/view/public/img/icon_new/system_authorization_new.png',
                'picture_selected' => 'app/shop/view/public/img/icon_new/system_authorization_select.png',
                'sort' => 12,
                'child_list' => [
                    [
                        'name' => 'WEBSITE_DEPLOYMENT',
                        'title' => '网站部署',
                        'url' => 'shop/config/sitedeploy',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => 'app/shop/view/public/img/icon_new/website_deploy_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/website_deploy_select.png',
                        'sort' => 1,
                        'child_list' => []
                    ],
                    [
                        'name' => 'SYSTEM_ADDON_ROOT',
                        'title' => '插件管理',
                        'url' => 'shop/system/addon',
                        'parent' => '',
                        'is_show' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/plug_management_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/plug_management_select.png',
                        'sort' => 2,
                        'child_list' => [],
                    ],
                    [
                        'name' => 'COPYRIGHT',
                        'title' => '版权设置',
                        'url' => 'shop/config/copyright',
                        'is_show' => 1,
                        'is_control' => 1,
                        'is_icon' => 0,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 2,
                    ],
                    [
                        'name' => 'CONFIG_SYSTEM_CACHE',
                        'title' => '缓存管理',
                        'url' => 'shop/system/cache',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 3,
                        'child_list' => [],
                    ],
                    [
                        'name' => 'AUTH_INFO',
                        'title' => '系统授权',
                        'url' => 'shop/upgrade/auth',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 4,
                    ],
                    [
                        'name' => 'UPGRADE_INFO',
                        'title' => '版本管理',
                        'url' => 'shop/upgrade/upgrade',
                        'is_show' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/system_upgrade_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/system_upgrade_select.png',
                        'sort' => 5,
                        'child_list' => [
                            [
                                'name' => 'UPGRADE_ACTION',
                                'title' => '升级操作 ',
                                'url' => 'shop/upgrade/upgradeAction',
                                'parent' => '',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],
                    [
                        'name' => 'PATCH_LISTS',
                        'title' => '补丁管理',
                        'url' => 'shop/upgrade/patchlists',
                        'is_show' => 1,
                        'sort' => 5,
                        'child_list' => [
                            [
                                'name' => 'PATCH_RES',
                                'title' => '补丁处理',
                                'url' => 'shop/upgrade/patchres',
                                'parent' => '',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'PATCH_ALERT_LISTS',
                                'title' => '补丁弹窗',
                                'url' => 'shop/upgrade/patchalertlists',
                                'parent' => '',
                                'is_show' => 0,
                                'is_control' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'PATCH_NOT_ALERT',
                                'title' => '补丁已知晓',
                                'url' => 'shop/upgrade/patchnotalert',
                                'parent' => '',
                                'is_show' => 0,
                                'is_control' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],
                    [
                        'name' => 'VERSION_LOG',
                        'title' => '更新日志',
                        'url' => 'shop/upgrade/versionLog',
                        'is_show' => 1,
                        'picture' => 'app/shop/view/public/img/icon_new/update_log_new.png',
                        'picture_selected' => 'app/shop/view/public/img/icon_new/update_log_select.png',
                        'sort' => 6,
                    ],
                    [
                        'name' => 'CONFIG_SYSTEM_DATABASE',
                        'title' => '数据库管理',
                        'url' => 'shop/system/database',
                        'is_show' => 1,
                        'picture' => '',
                        'picture_selected' => '',
                        'sort' => 7,
                        'child_list' => [
                            [
                                'name' => 'CONFIG_SYSTEM_DATABASE_LIST',
                                'title' => '数据备份',
                                'url' => 'shop/system/database',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 1,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_IMPORTLIST',
                                'title' => '数据还原',
                                'url' => 'shop/system/importlist',
                                'parent' => '',
                                'is_show' => 1,
                                'picture' => '',
                                'picture_selected' => '',
                                'sort' => 2,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_BACKUP',
                                'title' => '数据备份',
                                'url' => 'shop/system/backup',
                                'parent' => '',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_DELETEBACKUP',
                                'title' => '删除备份文件',
                                'url' => 'shop/system/deletebackup',
                                'parent' => '',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                            [
                                'name' => 'CONFIG_SYSTEM_TABLEREPAIR',
                                'title' => '数据表修复',
                                'url' => 'shop/system/tablerepair',
                                'parent' => '',
                                'is_show' => 0,
                                'type' => 'button',
                            ],
                        ],
                    ],

                ],
            ],
        ]
    ],

];
