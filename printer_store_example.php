<?php
/**
 * 打印机门店关联管理示例
 */

require_once __DIR__ . '/vendor/autoload.php';

$app = new think\App();
$app->initialize();

use addon\printer\model\Printer;

$printer = new Printer();

// 示例1: 设置打印机关联多个门店
$printer_id = 1;
$store_ids = [1, 2, 3]; // 门店ID数组
$site_id = 1;

$result1 = $printer->setPrinterStores($printer_id, $store_ids, $site_id);
echo "设置打印机关联门店:\n";
var_dump($result1);

// 示例2: 获取打印机关联的门店列表
$result2 = $printer->getPrinterStores($printer_id, $site_id);
echo "\n获取打印机关联的门店:\n";
var_dump($result2);

// 示例3: 根据门店ID获取可用的打印机列表
$store_id = 1;
$result3 = $printer->getPrinterListByStore($store_id, $site_id);
echo "\n根据门店获取打印机列表:\n";
var_dump($result3);

// 示例4: 添加单个打印机门店关联
$result4 = $printer->addPrinterStore($printer_id, 4, $site_id);
echo "\n添加单个关联:\n";
var_dump($result4);

// 示例5: 删除打印机门店关联
$result5 = $printer->removePrinterStore($printer_id, 4, $site_id);
echo "\n删除关联:\n";
var_dump($result5);
?>
