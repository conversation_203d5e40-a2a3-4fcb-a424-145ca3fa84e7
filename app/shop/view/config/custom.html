<style>
    .form-item-desc {
        margin-top: 10px;
        margin-left: 110px; /* 与layui-form-label的宽度对齐 */
        color: #999;
        font-size: 12px;
    }
    .textarea-custom {
        min-height: 80px;
    }
    .delivery-section {
        margin-bottom: 30px;
        border: 1px solid #e6e6e6;
        border-radius: 5px;
        padding: 20px;
        background-color: #fafafa;
    }
    .delivery-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        border-bottom: 2px solid #1E9FFF;
        padding-bottom: 8px;
    }
</style>

<div class="layui-form form-wrap">
    <!-- 同城配送配置 -->
    <div class="delivery-section" >
        <div class="delivery-title">同城配送 (local)</div>

        <div class="layui-form-item" >
            <label class="layui-form-label">配送费说明：</label>
            <div class="layui-input-block">
                <textarea name="local_delivery_fee_desc" placeholder="请输入同城配送的配送费说明" class="layui-textarea textarea-custom">{$config_info.local.delivery_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明同城配送的配送费计算方式和相关规则</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">服务费说明：</label>
            <div class="layui-input-block">
                <textarea name="local_service_fee_desc" placeholder="请输入同城配送的服务费说明" class="layui-textarea textarea-custom">{$config_info.local.service_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明同城配送的服务费收取标准和服务内容</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">包装费说明：</label>
            <div class="layui-input-block">
                <textarea name="local_package_fee_desc" placeholder="请输入同城配送的包装费说明" class="layui-textarea textarea-custom">{$config_info.local.package_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明同城配送的包装费收取原因和包装标准</div>
        </div>
    </div>

    <!-- 快递发货配置 -->
    <div class="delivery-section">
        <div class="delivery-title">快递发货 (express)</div>

        <div class="layui-form-item">
            <label class="layui-form-label">配送费说明：</label>
            <div class="layui-input-block">
                <textarea name="express_delivery_fee_desc" placeholder="请输入快递发货的配送费说明" class="layui-textarea textarea-custom">{$config_info.express.delivery_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明快递发货的配送费计算方式和相关规则</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">服务费说明：</label>
            <div class="layui-input-block">
                <textarea name="express_service_fee_desc" placeholder="请输入快递发货的服务费说明" class="layui-textarea textarea-custom">{$config_info.express.service_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明快递发货的服务费收取标准和服务内容</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">包装费说明：</label>
            <div class="layui-input-block">
                <textarea name="express_package_fee_desc" placeholder="请输入快递发货的包装费说明" class="layui-textarea textarea-custom">{$config_info.express.package_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明快递发货的包装费收取原因和包装标准</div>
        </div>
    </div>

    <!-- 门店自提配置 -->
    <div class="delivery-section">
        <div class="delivery-title">门店自提 (store)</div>

        <div class="layui-form-item">
            <label class="layui-form-label">配送费说明：</label>
            <div class="layui-input-block">
                <textarea name="store_delivery_fee_desc" placeholder="请输入门店自提的配送费说明" class="layui-textarea textarea-custom">{$config_info.store.delivery_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明门店自提的配送费计算方式和相关规则</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">服务费说明：</label>
            <div class="layui-input-block">
                <textarea name="store_service_fee_desc" placeholder="请输入门店自提的服务费说明" class="layui-textarea textarea-custom">{$config_info.store.service_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明门店自提的服务费收取标准和服务内容</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">包装费说明：</label>
            <div class="layui-input-block">
                <textarea name="store_package_fee_desc" placeholder="请输入门店自提的包装费说明" class="layui-textarea textarea-custom">{$config_info.store.package_fee_desc|default=''}</textarea>
            </div>
            <div class="form-item-desc">用于向用户说明门店自提的包装费收取原因和包装标准</div>
        </div>
    </div>

    <div class="form-row">
        <button class="layui-btn" lay-submit lay-filter="save">保存</button>
    </div>
</div>

<script>
layui.use(['form'], function(){
    var form = layui.form;
    
    // 监听提交
    form.on('submit(save)', function(data){
        var loading = layer.load();
        
        $.ajax({
            type: 'POST',
            url: ns.url("shop/config/custom"),
            data: data.field,
            dataType: 'JSON',
            success: function(res) {
                layer.close(loading);
                if (res.code >= 0) {
                    layer.msg('保存成功', {icon: 1});
                } else {
                    layer.msg(res.message || '保存失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loading);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        
        return false;
    });
});
</script>
