<style>
    .form-item-desc {
        margin-top: 10px;
        margin-left: 110px; /* 与layui-form-label的宽度对齐 */
        color: #999;
        font-size: 12px;
    }
    .textarea-custom {
        min-height: 100px;
    }
</style>

<div class="layui-form form-wrap">
    <div class="layui-form-item">
        <label class="layui-form-label">配送费说明：</label>
        <div class="layui-input-block">
            <textarea name="delivery_fee_desc" placeholder="请输入配送费说明" class="layui-textarea textarea-custom">{$config_info.delivery_fee_desc|default=''}</textarea>
        </div>
        <div class="form-item-desc">用于向用户说明配送费的计算方式和相关规则</div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">服务费说明：</label>
        <div class="layui-input-block">
            <textarea name="service_fee_desc" placeholder="请输入服务费说明" class="layui-textarea textarea-custom">{$config_info.service_fee_desc|default=''}</textarea>
        </div>
        <div class="form-item-desc">用于向用户说明服务费的收取标准和服务内容</div>
    </div>
    
    <div class="layui-form-item">
        <label class="layui-form-label">包装费说明：</label>
        <div class="layui-input-block">
            <textarea name="package_fee_desc" placeholder="请输入包装费说明" class="layui-textarea textarea-custom">{$config_info.package_fee_desc|default=''}</textarea>
        </div>
        <div class="form-item-desc">用于向用户说明包装费的收取原因和包装标准</div>
    </div>
    
    <div class="form-row">
        <button class="layui-btn" lay-submit lay-filter="save">保存</button>
    </div>
</div>

<script>
layui.use(['form'], function(){
    var form = layui.form;
    
    // 监听提交
    form.on('submit(save)', function(data){
        var loading = layer.load();
        
        $.ajax({
            type: 'POST',
            url: ns.url("shop/config/custom"),
            data: data.field,
            dataType: 'JSON',
            success: function(res) {
                layer.close(loading);
                if (res.code >= 0) {
                    layer.msg('保存成功', {icon: 1});
                } else {
                    layer.msg(res.message || '保存失败', {icon: 2});
                }
            },
            error: function() {
                layer.close(loading);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        
        return false;
    });
});
</script>
